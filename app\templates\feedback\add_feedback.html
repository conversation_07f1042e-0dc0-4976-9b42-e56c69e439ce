{% extends "base.html" %}

{% block title %}Add Feedback - {{ dataset.title }}{% endblock %}

{% block content %}
<div class="container">
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Home</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('datasets.metadata', dataset_id=dataset.id) }}">{{ dataset.title }}</a></li>
            <li class="breadcrumb-item active">Add Feedback</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>
                        {% if existing_feedback %}Update Your Feedback{% else %}Add Your Feedback{% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-muted">Dataset: {{ dataset.title }}</h6>
                        <p class="text-muted small">{{ dataset.description[:200] }}{% if dataset.description|length > 200 %}...{% endif %}</p>
                    </div>

                    <form method="POST">
                        <!-- Overall Rating -->
                        <div class="mb-4">
                            <label class="form-label"><strong>Overall Rating *</strong></label>
                            <div class="rating-input">
                                {% for i in range(1, 6) %}
                                <input type="radio" name="rating" value="{{ i }}" id="rating{{ i }}" 
                                       {% if existing_feedback and existing_feedback.rating == i %}checked{% endif %} required>
                                <label for="rating{{ i }}" class="rating-star">
                                    <i class="fas fa-star"></i>
                                    <span class="rating-text">{{ i }} Star{{ 's' if i != 1 else '' }}</span>
                                </label>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Detailed Ratings -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label class="form-label">Data Quality</label>
                                <select name="quality" class="form-select">
                                    <option value="">Select...</option>
                                    {% for i in range(1, 6) %}
                                    <option value="{{ i }}" {% if existing_feedback and existing_feedback.quality == i %}selected{% endif %}>
                                        {{ i }} - {% if i == 1 %}Poor{% elif i == 2 %}Fair{% elif i == 3 %}Good{% elif i == 4 %}Very Good{% else %}Excellent{% endif %}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Usefulness</label>
                                <select name="usefulness" class="form-select">
                                    <option value="">Select...</option>
                                    {% for i in range(1, 6) %}
                                    <option value="{{ i }}" {% if existing_feedback and existing_feedback.usefulness == i %}selected{% endif %}>
                                        {{ i }} - {% if i == 1 %}Not Useful{% elif i == 2 %}Slightly Useful{% elif i == 3 %}Moderately Useful{% elif i == 4 %}Very Useful{% else %}Extremely Useful{% endif %}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Satisfaction</label>
                                <select name="satisfaction" class="form-select">
                                    <option value="">Select...</option>
                                    {% for i in range(1, 6) %}
                                    <option value="{{ i }}" {% if existing_feedback and existing_feedback.satisfaction == i %}selected{% endif %}>
                                        {{ i }} - {% if i == 1 %}Very Dissatisfied{% elif i == 2 %}Dissatisfied{% elif i == 3 %}Neutral{% elif i == 4 %}Satisfied{% else %}Very Satisfied{% endif %}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <!-- Comment -->
                        <div class="mb-4">
                            <label for="comment" class="form-label">Your Review (Optional)</label>
                            <textarea name="comment" id="comment" class="form-control" rows="4" 
                                      placeholder="Share your experience with this dataset...">{% if existing_feedback %}{{ existing_feedback.comment }}{% endif %}</textarea>
                            <div class="form-text">Help others by sharing what you liked or didn't like about this dataset.</div>
                        </div>

                        <!-- Feedback Type -->
                        <div class="mb-4">
                            <label class="form-label">Feedback Type</label>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="feedback_type" value="rating" id="type_rating" 
                                               {% if not existing_feedback or existing_feedback.feedback_type == 'rating' %}checked{% endif %}>
                                        <label class="form-check-label" for="type_rating">Rating</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="feedback_type" value="comment" id="type_comment"
                                               {% if existing_feedback and existing_feedback.feedback_type == 'comment' %}checked{% endif %}>
                                        <label class="form-check-label" for="type_comment">Review</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="feedback_type" value="suggestion" id="type_suggestion"
                                               {% if existing_feedback and existing_feedback.feedback_type == 'suggestion' %}checked{% endif %}>
                                        <label class="form-check-label" for="type_suggestion">Suggestion</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="feedback_type" value="issue" id="type_issue"
                                               {% if existing_feedback and existing_feedback.feedback_type == 'issue' %}checked{% endif %}>
                                        <label class="form-check-label" for="type_issue">Issue</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Anonymous Option -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_anonymous" id="is_anonymous"
                                       {% if existing_feedback and existing_feedback.is_anonymous %}checked{% endif %}>
                                <label class="form-check-label" for="is_anonymous">
                                    Submit anonymously
                                </label>
                                <div class="form-text">Your username will not be displayed with this feedback.</div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('datasets.view_metadata', dataset_id=dataset.id) }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-{% if existing_feedback %}edit{% else %}plus{% endif %} me-1"></i>
                                {% if existing_feedback %}Update Feedback{% else %}Submit Feedback{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.rating-input {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
    gap: 0.5rem;
}

.rating-input input[type="radio"] {
    display: none;
}

.rating-star {
    cursor: pointer;
    color: #ddd;
    font-size: 1.5rem;
    transition: color 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rating-star:hover,
.rating-star:hover ~ .rating-star {
    color: #ffc107;
}

.rating-input input[type="radio"]:checked ~ .rating-star {
    color: #ffc107;
}

.rating-text {
    font-size: 0.9rem;
    font-weight: normal;
}

@media (max-width: 768px) {
    .rating-input {
        flex-direction: column;
    }
    
    .rating-star {
        justify-content: flex-start;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Rating interaction
    const ratingInputs = document.querySelectorAll('input[name="rating"]');
    const ratingStars = document.querySelectorAll('.rating-star');
    
    ratingInputs.forEach((input, index) => {
        input.addEventListener('change', function() {
            updateStarDisplay();
        });
    });
    
    function updateStarDisplay() {
        const checkedRating = document.querySelector('input[name="rating"]:checked');
        if (checkedRating) {
            const rating = parseInt(checkedRating.value);
            ratingStars.forEach((star, index) => {
                if (index < rating) {
                    star.style.color = '#ffc107';
                } else {
                    star.style.color = '#ddd';
                }
            });
        }
    }
    
    // Initialize star display
    updateStarDisplay();
});
</script>
{% endblock %}
