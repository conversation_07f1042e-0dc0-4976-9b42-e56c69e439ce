{"export_info": {"export_format": "JSON", "export_date": "2025-06-23T02:10:59.046730", "generated_by": " Metadata Harvesting System", "version": "2.0"}, "basic_info": {"id": "6855bcf42ad862c3358bfdbe", "title": "Dependencies", "source": "File Upload", "category": "General", "data_type": "", "format": "csv", "file_size": null, "license": null, "created_at": "2025-06-20T19:56:36.886000", "updated_at": "2025-06-20T19:57:34.626000", "source_url": "", "persistent_identifier": null, "encoding_format": null, "status": "completed"}, "description": {"main_description": "This dataset, titled 'Dependencies', represents a comprehensive collection of structured data designed for advanced analytical and research applications. The dataset encompasses 14,902 records systematically organized across 3 distinct fields, providing a robust foundation for statistical analysis and data mining operations. The dataset structure includes the following fields: project, required, dep. The dataset incorporates textual data elements enabling qualitative analysis and natural language processing applications. Primary thematic elements include: pytest, google, tensorflow, sphinx, importlib, ipython, among 4 additional conceptual dimensions. This dataset is particularly well-suited for , and large-scale statistical analysis and machine learning model development. The structured nature of this dataset, combined with its comprehensive field coverage, makes it a valuable resource for researchers, analysts, and data scientists seeking to derive meaningful insights through rigorous analytical methodologies.", "structured_description": null, "auto_generated_description": null}, "data_statistics": {"record_count": 14902, "field_count": 3, "field_names": ["project", " required", " dep"], "data_types": ["object", " bool"], "data_distribution_types": null}, "metadata_fields": {"tags": ["auth", " core", " descriptive", " google", " importlib", " ipython", " metrics", " numerical", " organizational", " personal", " pytest", " qualitative", " quantitative", " requests", " setuptools", " sphinx", " tensorflow", " textual"], "keywords": ["[\"pytest\"", " \"google\"", " \"tensorflow\"", " \"sphinx\"", " \"importlib\"", " \"ipython\"", " \"requests\"", " \"setuptools\"", " \"core\"", " \"auth\"", " \"jupyter\"", " \"metadata\"", " \"sanic\"", " \"client\"", " \"cloud\"", " \"Dependencies\"", " \"project\"", " \"required\"", " \"dep\"]"], "use_cases": ["\"data analysis", " research studies", " machine learning\""], "entities": null, "sentiment": null}, "quality_assessment": {"overall_quality_score": 56.77196969696969, "completeness": 28.787878787878782, "consistency": 64.0, "accuracy": 79.0, "timeliness": 75.5, "conformity": 34.833333333333336, "integrity": 81.0, "issues": ["Missing required field: description", "Missing required field: source", "Inconsistent field definition for project", "Inconsistent field definition for required", "Inconsistent field definition for dep", "Schema.org metadata not defined"], "recommendations": ["Add description to improve dataset completeness", "Add source to improve dataset completeness", "Consider adding these fields to enhance completeness: sample_data, schema, tags", "Specify update frequency to improve timeliness assessment", "Define Schema.org metadata to improve standards conformity", "Assign a persistent identifier to improve findability", "Ensure the dataset is indexed in searchable resources", "Provide a standard access URL to improve accessibility", "Use FAIR vocabularies to improve interoperability", "Include qualified references to related datasets", "Specify a clear license to improve reusability", "Add detailed provenance information"], "assessment_date": "2025-06-20T19:56:50.835000"}, "fair_compliance": {"overall_score": 57.99999999999999, "is_compliant": false, "findable_score": 32.0, "accessible_score": 100.0, "interoperable_score": 32.0, "reusable_score": 68.0}, "standards_compliance": {"schema_org_compliant": false, "dublin_core": {}, "dcat_metadata": {}, "json_ld": {}}, "visualizations": {"quality_metrics": {"type": "quality_metrics", "data": {"overall_score": 56.77196969696969, "completeness": 28.787878787878782, "consistency": 64.0, "accuracy": 79.0, "fair_compliant": false}, "chart_config": {"type": "radar", "title": "Data Quality Assessment", "description": "Comprehensive quality metrics for the dataset"}}, "data_overview": {"type": "data_overview", "data": {"record_count": 14902, "field_count": 3, "data_size": "14902 rows × 3 columns", "estimated_size_mb": 0.34}, "chart_config": {"type": "info_cards", "title": "Dataset Overview", "description": "Basic statistics about the dataset structure"}}, "field_analysis": {"type": "field_analysis", "data": {"type_distribution": {"object": 2, "bool": 1}, "numeric_fields": 0, "text_fields": 0, "categorical_fields": 0, "datetime_fields": 0}, "chart_config": {"type": "pie", "title": "Field Type Distribution", "description": "Distribution of data types across dataset fields"}}, "keyword_cloud": {"type": "keyword_cloud", "data": {"keywords": [{"text": "pytest", "weight": 21}, {"text": "google", "weight": 20}, {"text": "tensorflow", "weight": 19}, {"text": "sphinx", "weight": 18}, {"text": "importlib", "weight": 17}, {"text": "ipython", "weight": 16}, {"text": "requests", "weight": 15}, {"text": "setuptools", "weight": 14}, {"text": "core", "weight": 13}, {"text": "auth", "weight": 12}, {"text": "jup<PERSON><PERSON>", "weight": 11}, {"text": "metadata", "weight": 10}, {"text": "sanic", "weight": 9}, {"text": "client", "weight": 8}, {"text": "cloud", "weight": 7}], "total_keywords": 15}, "chart_config": {"type": "wordcloud", "title": "Content Keywords", "description": "Most relevant keywords extracted from dataset content"}}, "metadata_completeness": {"type": "metadata_completeness", "data": {"completeness_fields": {"title": true, "description": true, "source": true, "category": true, "tags": true, "quality_assessed": true, "schema_defined": false}, "completed_count": 6, "total_count": 7, "completeness_percentage": 85.7}, "chart_config": {"type": "progress_bar", "title": "Metadata Completeness", "description": "85.7% of metadata fields are complete"}}, "data_distribution": {"type": "histogram", "data": {"bins": [], "frequencies": []}, "chart_config": {"type": "histogram", "title": "Data Distribution", "description": "No numeric data found"}}, "correlation_analysis": {"type": "heatmap", "data": {"matrix": [], "labels": {}}, "chart_config": {"type": "heatmap", "title": "Correlation Analysis", "description": "Insufficient numeric data for correlation"}}, "trend_analysis": {"type": "line", "data": {"labels": ["Record 0", "Record 10", "Record 20", "Record 30", "Record 40", "Record 50", "Record 60", "Record 70", "Record 80", "Record 90"], "values": [87.0, 83.1, 87.2, 83.3, 87.4, 83.5, 87.6, 83.7, 87.8, 83.9], "x": [0, 10, 20, 30, 40, 50, 60, 70, 80, 90], "y": [87.0, 83.1, 87.2, 83.3, 87.4, 83.5, 87.6, 83.7, 87.8, 83.9]}, "chart_config": {"type": "line", "title": "Data Quality Trend", "description": "Quality trend across 10 sample points"}}, "generated_at": "2025-06-20T20:57:34.614601", "visualization_version": "3.0"}, "health_report": null, "ai_compliance": null, "processing_metadata": null, "python_examples": {"basic_loading": "# Load Dependencies dataset\nimport pandas as pd\nimport numpy as np\n\n# Load the dataset\ndf = pd.read_csv('dependencies.csv')\n\n# Basic information\nprint(f\"Dataset shape: {df.shape}\")\nprint(f\"Columns: {list(df.columns)}\")\nprint(df.head())", "data_exploration": "# Data exploration for Dependencies\nimport matplotlib.pyplot as plt\nimport seaborn as sns\n\n# Dataset overview\nprint(df.info())\nprint(df.describe())\n\n# Check for missing values\nprint(df.isnull().sum())\n\n# Basic visualizations\nplt.figure(figsize=(10, 6))\n# Analyze required\n# Analyze dep"}}