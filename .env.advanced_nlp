
# Advanced NLP Models Configuration
# Add these to your .env file or environment variables

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o  # or gpt-4-turbo, gpt-3.5-turbo

# Anthropic Configuration  
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-5-sonnet-20241022  # or claude-3-opus-20240229

# Google Gemini Configuration
GOOGLE_API_KEY=your_google_api_key_here
GEMINI_MODEL=gemini-1.5-pro  # or gemini-1.5-flash for faster responses

# Free AI Models Configuration for Enhanced Description Generation
# These models provide high-quality dataset descriptions with FLAN-T5 Base as fallback

# Primary Free AI Models (Recommended)
MISTRAL_API_KEY=l9GKHibNgRJrrG3gJX1YgFUmBTNb7Bd8  # Get free key from: https://console.mistral.ai/
GROQ_API_KEY=********************************************************  # Get free key from: https://console.groq.com/

# Additional Free AI Models (Optional)
TOGETHER_API_KEY=your_together_key_here  # Get free credits from: https://together.ai/
HF_API_KEY=*************************************  # Get free key from: https://huggingface.co/settings/tokens

# Model Configuration
# Priority: Free AI (Mistral/Groq) → FLAN-T5 Base → Local NLP
USE_FREE_AI=true  # Set to false to skip free AI and use FLAN-T5 directly
USE_FLAN_T5=true  # FLAN-T5 Base model for offline description generation

# Text Cleaning Configuration
CLEAN_SPECIAL_CHARACTERS=true  # Remove markdown and special characters from descriptions
ENSURE_SENTENCE_STRUCTURE=true  # Ensure descriptions end with proper punctuation
