{% extends "base.html" %}

{% block title %}Login - Dataset Metadata Manager{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-5">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white text-center py-3">
                    <h4 class="mb-0"><i class="fas fa-sign-in-alt me-2"></i>Log In</h4>
                </div>
                <div class="card-body p-4">
                    <form method="POST" action="{{ url_for('auth.login') }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.username.label(class="form-label") }}
                            {% if form.username.errors %}
                                {{ form.username(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.username(class="form-control", placeholder="Enter your username") }}
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.password.label(class="form-label") }}
                            {% if form.password.errors %}
                                {{ form.password(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.password(class="form-control", placeholder="Enter your password") }}
                            {% endif %}
                        </div>
                        
                        <div class="mb-3 form-check">
                            {{ form.remember_me(class="form-check-input") }}
                            {{ form.remember_me.label(class="form-check-label") }}
                        </div>
                        
                        <div class="d-grid">
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center py-3">
                    <p class="mb-0">Don't have an account? <a href="{{ url_for('auth.register') }}" class="text-primary">Register</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}