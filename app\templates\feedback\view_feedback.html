{% extends "base.html" %}

{% block title %}Reviews - {{ dataset.title }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Home</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('datasets.metadata', dataset_id=dataset.id) }}">{{ dataset.title }}</a></li>
            <li class="breadcrumb-item active">Reviews</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-star me-2"></i>
                        Reviews for "{{ dataset.title }}"
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Rating Summary -->
                    {% if feedback_summary %}
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h2 class="text-warning mb-1">{{ feedback_summary.average_rating }}</h2>
                                <div class="rating-stars mb-2">
                                    {% for i in range(1, 6) %}
                                        {% if i <= feedback_summary.average_rating %}
                                            <i class="fas fa-star text-warning"></i>
                                        {% else %}
                                            <i class="fas fa-star text-muted"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <p class="text-muted">{{ feedback_summary.total_reviews }} review{{ 's' if feedback_summary.total_reviews != 1 else '' }}</p>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <h6 class="text-muted mb-3">Rating Distribution</h6>
                            {% for i in range(5, 0, -1) %}
                            <div class="d-flex align-items-center mb-1">
                                <span class="me-2">{{ i }}★</span>
                                <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                    {% set rating_count = feedback_summary.rating_distribution[i] if feedback_summary.rating_distribution else 0 %}
                                    {% set percentage = (rating_count / feedback_summary.total_reviews * 100) if feedback_summary.total_reviews > 0 else 0 %}
                                    <div class="progress-bar bg-warning" role="progressbar" 
                                         style="width: {{ percentage }}%"></div>
                                </div>
                                <small class="text-muted">{{ rating_count }}</small>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Action Buttons -->
                    <div class="mb-4">
                        {% if current_user.is_authenticated %}
                        <a href="{{ url_for('feedback.add_feedback', dataset_id=dataset.id) }}" 
                           class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            Write a Review
                        </a>
                        {% endif %}
                        <a href="{{ url_for('datasets.metadata', dataset_id=dataset.id) }}" 
                           class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            Back to Dataset
                        </a>
                    </div>

                    <!-- Reviews List -->
                    <div class="reviews-list">
                        {% if all_feedback %}
                            {% for feedback in all_feedback %}
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <div class="rating-stars mb-1">
                                                {% for i in range(1, 6) %}
                                                    {% if i <= feedback.rating %}
                                                        <i class="fas fa-star text-warning"></i>
                                                    {% else %}
                                                        <i class="fas fa-star text-muted"></i>
                                                    {% endif %}
                                                {% endfor %}
                                            </div>
                                            <small class="text-muted">
                                                by {{ feedback.user.username if not feedback.is_anonymous else 'Anonymous' }} • 
                                                {{ feedback.created_at.strftime('%B %d, %Y') }}
                                            </small>
                                        </div>
                                        {% if current_user.is_authenticated and current_user.id == feedback.user.id %}
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                    type="button" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <form method="POST" action="{{ url_for('feedback.delete_feedback', feedback_id=feedback.id) }}" 
                                                          onsubmit="return confirm('Are you sure you want to delete this review?')">
                                                        <button type="submit" class="dropdown-item text-danger">
                                                            <i class="fas fa-trash me-1"></i>Delete
                                                        </button>
                                                    </form>
                                                </li>
                                            </ul>
                                        </div>
                                        {% endif %}
                                    </div>
                                    
                                    {% if feedback.comment %}
                                    <p class="mb-2">{{ feedback.comment }}</p>
                                    {% endif %}
                                    
                                    <!-- Additional feedback details -->
                                    {% if feedback.satisfaction or feedback.usefulness or feedback.quality %}
                                    <div class="row mt-3">
                                        {% if feedback.satisfaction %}
                                        <div class="col-md-4">
                                            <small class="text-muted">Satisfaction:</small>
                                            <div class="rating-stars">
                                                {% for i in range(1, 6) %}
                                                    {% if i <= feedback.satisfaction %}
                                                        <i class="fas fa-star text-warning small"></i>
                                                    {% else %}
                                                        <i class="fas fa-star text-muted small"></i>
                                                    {% endif %}
                                                {% endfor %}
                                            </div>
                                        </div>
                                        {% endif %}
                                        {% if feedback.usefulness %}
                                        <div class="col-md-4">
                                            <small class="text-muted">Usefulness:</small>
                                            <div class="rating-stars">
                                                {% for i in range(1, 6) %}
                                                    {% if i <= feedback.usefulness %}
                                                        <i class="fas fa-star text-warning small"></i>
                                                    {% else %}
                                                        <i class="fas fa-star text-muted small"></i>
                                                    {% endif %}
                                                {% endfor %}
                                            </div>
                                        </div>
                                        {% endif %}
                                        {% if feedback.quality %}
                                        <div class="col-md-4">
                                            <small class="text-muted">Quality:</small>
                                            <div class="rating-stars">
                                                {% for i in range(1, 6) %}
                                                    {% if i <= feedback.quality %}
                                                        <i class="fas fa-star text-warning small"></i>
                                                    {% else %}
                                                        <i class="fas fa-star text-muted small"></i>
                                                    {% endif %}
                                                {% endfor %}
                                            </div>
                                        </div>
                                        {% endif %}
                                    </div>
                                    {% endif %}
                                    
                                    <!-- Helpful button -->
                                    {% if current_user.is_authenticated and current_user.id != feedback.user.id %}
                                    <div class="mt-3">
                                        <button class="btn btn-sm btn-outline-success" 
                                                onclick="markHelpful('{{ feedback.id }}')">
                                            <i class="fas fa-thumbs-up me-1"></i>
                                            Helpful ({{ feedback.helpful_count or 0 }})
                                        </button>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No reviews yet</h5>
                                <p class="text-muted">Be the first to review this dataset!</p>
                                {% if current_user.is_authenticated %}
                                <a href="{{ url_for('feedback.add_feedback', dataset_id=dataset.id) }}" 
                                   class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>
                                    Write the First Review
                                </a>
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function markHelpful(feedbackId) {
    fetch(`/feedback/${feedbackId}/helpful`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || ''
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload(); // Refresh to show updated helpful count
        } else {
            alert(data.message || 'Error marking as helpful');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error marking as helpful');
    });
}
</script>
{% endblock %}
