{% extends "base.html" %}

{% block title %}Dataset - {{ dataset.title }}{% endblock %}

{% block content %}
<div class="container my-4" data-dataset-id="{{ dataset.id }}">
    <div class="row mb-4">
        <div class="col">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('datasets.list') }}">Datasets</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ dataset.title }}</li>
                </ol>
            </nav>
            <h1 class="mb-0">
                {{ dataset.title }}
                {% if dataset.is_collection %}
                <span class="badge bg-info ms-2">Collection</span>
                {% endif %}
                {% if dataset.auto_generated_title %}
                <span class="badge bg-secondary ms-2">Auto-generated</span>
                {% endif %}
            </h1>
            <p class="text-muted">Added on {{ dataset.created_at.strftime('%B %d, %Y') }}</p>
        </div>
    </div>

    <!-- Real-time Processing Status Alert -->
    {% if dataset.status == 'processing' %}
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-info" role="alert">
                <div class="d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm me-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="alert-heading mb-1">Processing in Progress</h6>
                        <p class="mb-2" id="processing-message">Dataset is being processed automatically...</p>
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated bg-info"
                                 role="progressbar"
                                 id="processing-progress"
                                 style="width: 0%"
                                 aria-valuenow="0"
                                 aria-valuemin="0"
                                 aria-valuemax="100">
                                <span id="progress-percent">0%</span>
                            </div>
                        </div>
                        <small class="text-muted mt-1 d-block">
                            Status: <span id="processing-status">Starting...</span>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Details</h5>
                    {% if current_user.is_authenticated and (not dataset.user or dataset.user.id == current_user.id or current_user.is_admin) %}
                    <div>
                        <a href="{{ url_for('datasets.edit', dataset_id=dataset.id) }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                        {% if quality %}
                        <div class="dropdown d-inline-block">
                            <button class="btn btn-sm btn-outline-info dropdown-toggle" type="button" id="reportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-file-medical-alt"></i> Health Report
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="reportDropdown">
                                <li><a class="dropdown-item" href="{{ url_for('reports.view_report', dataset_id=dataset.id, format='html') }}" target="_blank">
                                    <i class="fas fa-file-code me-2"></i> View HTML Report
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('reports.view_report', dataset_id=dataset.id, format='pdf') }}">
                                    <i class="fas fa-file-pdf me-2"></i> Download PDF Report
                                </a></li>
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12 mb-4">
                            {% if dataset.is_collection %}
                                <h5>Collection Description</h5>
                                {% if dataset.structured_description %}
                                    {% set collection_metadata = dataset.structured_description | from_json %}
                                    {% if collection_metadata.overview %}
                                        <div class="collection-overview mb-3">
                                            <p>{{ collection_metadata.overview.text }}</p>
                                            {% if collection_metadata.detailed_analysis %}
                                                <div class="mt-2">
                                                    <small class="text-muted">{{ collection_metadata.detailed_analysis }}</small>
                                                </div>
                                            {% endif %}
                                        </div>

                                        {% if collection_metadata.thematic_summary %}
                                            <div class="thematic-summary">
                                                <h6 class="text-info">Thematic Analysis:</h6>
                                                <p class="text-muted">{{ collection_metadata.thematic_summary }}</p>
                                            </div>
                                        {% endif %}
                                    {% endif %}
                                {% else %}
                                    <p>{{ dataset.description or 'Collection description will be generated during processing.' }}</p>
                                {% endif %}
                            {% else %}
                                {% if dataset.structured_description %}
                                    {% set structured_description = dataset.structured_description | from_json %}
                                    {% include 'datasets/structured_description.html' %}
                                {% else %}
                                    <h5>Description</h5>
                                    <p>{{ dataset.description or 'No description provided' }}</p>
                                {% endif %}
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <h6>Source</h6>
                            <p class="mb-0">{{ dataset.source }}</p>
                        </div>

                        <div class="col-md-6 mb-3">
                            <h6>Category</h6>
                            <p class="mb-0">{{ dataset.category or 'Uncategorized' }}</p>
                        </div>

                        <div class="col-md-6 mb-3">
                            <h6>Data Type</h6>
                            <p class="mb-0">{{ dataset.data_type or 'Not specified' }}</p>
                        </div>

                        <div class="col-md-6 mb-3">
                            <h6>Format</h6>
                            <p class="mb-0">{{ dataset.format or 'Unknown' }}</p>
                        </div>

                        <div class="col-md-6 mb-3">
                            <h6>Size</h6>
                            <p class="mb-0">{{ dataset.size or 'Unknown' }}</p>
                        </div>

                        <div class="col-md-6 mb-3">
                            <h6>Record Count</h6>
                            <p class="mb-0">{{ dataset.record_count or 'Unknown' }}</p>
                        </div>

                        <div class="col-md-12 mb-3">
                            <h6>Source URL</h6>
                            {% if dataset.source_url %}
                            <a href="{{ dataset.source_url }}" target="_blank" class="mb-0 text-break">
                                {{ dataset.source_url }}
                                <i class="fas fa-external-link-alt ms-1 small"></i>
                            </a>
                            {% else %}
                            <p class="mb-0">Not provided</p>
                            {% endif %}
                        </div>

                        <div class="col-md-12">
                            <h6>Tags</h6>
                            <div>
                                {% if dataset.tags_list %}
                                {% for tag in dataset.tags_list %}
                                <span class="badge bg-primary me-1">{{ tag }}</span>
                                {% endfor %}
                                {% else %}
                                <p class="mb-0">No tags</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Collection Files Section -->
            {% if dataset.is_collection and dataset.collection_files %}
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-archive me-2"></i>
                        Collection Contents
                    </h5>
                </div>
                <div class="card-body">
                    {% set collection_files = dataset.collection_files | from_json %}
                    {% if collection_files %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>File Name</th>
                                    <th>Type</th>
                                    <th>Size</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for file in collection_files %}
                                <tr>
                                    <td>
                                        <i class="fas fa-file-{% if file.extension == 'csv' %}csv{% elif file.extension == 'json' %}code{% elif file.extension == 'xml' %}code{% else %}alt{% endif %} me-2"></i>
                                        {{ file.name }}
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ file.extension.upper() }}</span>
                                    </td>
                                    <td>{{ (file.size / 1024 / 1024) | round(2) }} MB</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            This collection contains {{ collection_files|length }} dataset files.
                        </small>
                    </div>
                    {% else %}
                    <p class="text-muted">Collection file information not available.</p>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>

        <div class="col-md-4">
            <div class="row">
                <!-- Quality Score Card -->
                <div class="col-12 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Quality Score</h5>
                        </div>
                        <div class="card-body text-center">
                            {% if quality %}
                            <div class="quality-score-circle
                                {% if quality.quality_score >= 80 %}bg-success{% elif quality.quality_score >= 60 %}bg-info{% elif quality.quality_score >= 40 %}bg-warning{% else %}bg-danger{% endif %}">
                                <h2 class="mb-0">{{ quality.quality_score|round|int }}</h2>
                                <p class="mb-0 small">out of 100</p>
                            </div>
                            <p class="mt-3 mb-0">
                                {% if quality.fair_compliant %}
                                <span class="badge bg-success">FAIR Compliant</span>
                                {% else %}
                                <span class="badge bg-warning">Not FAIR Compliant</span>
                                {% endif %}

                                {% if quality.schema_org_compliant %}
                                <span class="badge bg-success">Schema.org Compliant</span>
                                {% else %}
                                <span class="badge bg-warning">Not Schema.org Compliant</span>
                                {% endif %}
                            </p>
                            <div class="mt-3">
                                <a href="{{ url_for('datasets.quality', dataset_id=dataset.id) }}" class="btn btn-primary btn-sm me-2">
                                    <i class="fas fa-chart-bar me-1"></i>
                                    Quality Details
                                </a>
                                {% if dataset.fair_compliant or dataset.fair_metadata %}
                                <a href="{{ url_for('datasets.fair_compliance', dataset_id=dataset.id) }}" class="btn btn-success btn-sm">
                                    <i class="fas fa-balance-scale me-1"></i>
                                    FAIR Report
                                </a>
                                {% endif %}
                            </div>
                            {% else %}
                            <div class="py-4">
                                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                <p>Quality has not been assessed yet.</p>
                                <a href="{{ url_for('datasets.assess_quality', dataset_id=dataset.id) }}" class="btn btn-primary">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    Assess Quality
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Status Card -->
                <div class="col-12 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Status</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span>Processing Status:</span>
                                <span class="badge
                                    {% if dataset.status == 'completed' %}bg-success
                                    {% elif dataset.status == 'processing' %}bg-info
                                    {% elif dataset.status == 'pending' %}bg-warning
                                    {% elif dataset.status == 'failed' %}bg-danger
                                    {% else %}bg-secondary{% endif %}">
                                    {{ dataset.status|capitalize }}
                                </span>
                            </div>

                            {% if processing and processing.status == 'processing' %}
                            <div class="mb-3">
                                <label class="form-label d-flex justify-content-between mb-1">
                                    <span>Progress:</span>
                                    <span>{{ processing.progress|round|int }}%</span>
                                </label>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: {{ processing.progress }}%"></div>
                                </div>
                                {% if processing.estimated_completion_time %}
                                <div class="text-end mt-1">
                                    <small class="text-muted">Est. completion: {{ processing.estimated_completion_time }}</small>
                                </div>
                                {% endif %}
                            </div>
                            {% endif %}

                            {% if dataset.status != 'processing' and dataset.status != 'pending' %}
                            <form action="{{ url_for('datasets.start_processing', dataset_id=dataset.id) }}" method="post" class="mt-3">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                <button type="submit" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-sync-alt me-2"></i>
                                    Start Processing
                                </button>
                            </form>
                            {% elif dataset.status == 'processing' %}
                            <div class="mt-3">
                                <button type="button" class="btn btn-outline-info w-100" onclick="window.progressTracker?.checkProgress()">
                                    <i class="fas fa-refresh me-2"></i>
                                    Refresh Progress
                                </button>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Metadata Card -->
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Metadata</h5>
                        </div>
                        <div class="card-body">
                            <p>
                                {% if dataset.schema_org %}
                                <i class="fas fa-check-circle text-success me-2"></i>
                                Schema.org metadata available
                                {% else %}
                                <i class="fas fa-times-circle text-danger me-2"></i>
                                Schema.org metadata not available
                                {% endif %}
                            </p>

                            <div class="d-grid gap-2">
                                <a href="{{ url_for('datasets.metadata', dataset_id=dataset.id) }}" class="btn btn-primary">
                                    <i class="fas fa-file-code me-2"></i>
                                    View Metadata
                                </a>
                                {% if dataset.visualizations %}
                                <a href="{{ url_for('datasets.visualizations', dataset_id=dataset.id) }}" class="btn btn-outline-info">
                                    <i class="fas fa-chart-line me-2"></i>
                                    View Visualizations
                                </a>
                                {% endif %}

                                <!-- Download Button -->
                                <a href="{{ url_for('datasets.download_dataset', dataset_id=dataset.id) }}" class="btn btn-success">
                                    <i class="fas fa-download me-2"></i>
                                    {% if dataset.is_collection %}
                                        Download Collection (ZIP)
                                    {% else %}
                                        Download Dataset (ZIP)
                                    {% endif %}
                                </a>

                                {% if dataset.is_collection and dataset.collection_id %}
                                <a href="{{ url_for('datasets.download_collection', collection_id=dataset.collection_id) }}" class="btn btn-outline-success">
                                    <i class="fas fa-archive me-2"></i>
                                    Download Full Collection (ZIP)
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete the dataset "{{ dataset.title }}"? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{{ url_for('datasets.delete', dataset_id=dataset.id) }}" method="post">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .quality-score-circle {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: #fff;
        margin: 0 auto;
    }
</style>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="{{ url_for('static', filename='js/progress-tracker.js') }}"></script>
{% endblock %}