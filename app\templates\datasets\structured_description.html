<!-- Structured Description Component -->
<div class="structured-description">
    {% if structured_description %}
        {% set desc = structured_description %}
        
        <!-- Overview Section -->
        {% if desc.overview %}
            <div class="description-section mb-4">
                <div class="row">
                    <div class="col-lg-8">
                        <h5 class="section-title">
                            <i class="fas fa-info-circle text-primary me-2"></i>
                            Overview
                        </h5>
                        {% if desc.overview.text %}
                            <p class="section-content">{{ desc.overview.text }}</p>
                        {% endif %}
                    </div>
                    
                    {% if desc.overview.statistics %}
                        <div class="col-lg-4">
                            <div class="stats-card bg-light p-3 rounded">
                                <h6 class="mb-3">Quick Stats</h6>
                                {% if desc.overview.statistics.record_count %}
                                    <div class="stat-item mb-2">
                                        <span class="stat-label">Records:</span>
                                        <span class="stat-value fw-bold">{{ "{:,}".format(desc.overview.statistics.record_count) }}</span>
                                    </div>
                                {% endif %}
                                {% if desc.overview.statistics.field_count %}
                                    <div class="stat-item mb-2">
                                        <span class="stat-label">Fields:</span>
                                        <span class="stat-value fw-bold">{{ desc.overview.statistics.field_count }}</span>
                                    </div>
                                {% endif %}
                                {% if desc.overview.size_category %}
                                    <div class="stat-item">
                                        <span class="stat-label">Size:</span>
                                        <span class="badge bg-{{ 'success' if desc.overview.size_category in ['small', 'medium'] else 'warning' if desc.overview.size_category == 'large' else 'danger' }}">
                                            {{ desc.overview.size_category.replace('_', ' ').title() }}
                                        </span>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        {% endif %}

        <!-- Data Structure Section -->
        {% if desc.data_structure %}
            <div class="description-section mb-4">
                <h5 class="section-title">
                    <i class="fas fa-table text-info me-2"></i>
                    Data Structure
                </h5>
                <div class="row">
                    <div class="col-md-12">
                        {% if desc.data_structure.text %}
                            <p class="section-content">{{ desc.data_structure.text }}</p>
                        {% elif desc.data_structure %}
                            <p class="section-content">{{ desc.data_structure }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- Statistical Insights Section -->
        {% if desc.statistical_insights %}
            <div class="description-section mb-4">
                <h5 class="section-title">
                    <i class="fas fa-chart-bar text-success me-2"></i>
                    Statistical Insights
                </h5>
                <div class="row">
                    <div class="col-md-12">
                        {% if desc.statistical_insights.text %}
                            <p class="section-content">{{ desc.statistical_insights.text }}</p>
                        {% elif desc.statistical_insights %}
                            <p class="section-content">{{ desc.statistical_insights }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- Domain and Use Cases Section -->
        {% if desc.domain_and_use_cases %}
            <div class="description-section mb-4">
                <h5 class="section-title">
                    <i class="fas fa-lightbulb text-warning me-2"></i>
                    Domain & Use Cases
                </h5>
                <div class="row">
                    <div class="col-md-12">
                        {% if desc.domain_and_use_cases.text %}
                            <p class="section-content">{{ desc.domain_and_use_cases.text }}</p>
                        {% elif desc.domain_and_use_cases %}
                            <p class="section-content">{{ desc.domain_and_use_cases }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- Quality Aspects Section -->
        {% if desc.quality_aspects %}
            <div class="description-section mb-4">
                <h5 class="section-title">
                    <i class="fas fa-shield-alt text-primary me-2"></i>
                    Quality & Compliance
                </h5>
                <div class="row">
                    <div class="col-md-12">
                        {% if desc.quality_aspects.text %}
                            <p class="section-content">{{ desc.quality_aspects.text }}</p>
                        {% elif desc.quality_aspects %}
                            <p class="section-content">{{ desc.quality_aspects }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- Usage Recommendations Section -->
        {% if desc.usage_recommendations %}
            <div class="description-section mb-4">
                <h5 class="section-title">
                    <i class="fas fa-thumbs-up text-success me-2"></i>
                    Usage Recommendations
                </h5>
                <div class="row">
                    <div class="col-md-12">
                        {% if desc.usage_recommendations.text %}
                            <div class="alert alert-info">
                                <p class="mb-0">{{ desc.usage_recommendations.text }}</p>
                            </div>
                        {% elif desc.usage_recommendations %}
                            <div class="alert alert-info">
                                <p class="mb-0">{{ desc.usage_recommendations }}</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- Metadata Footer -->
        {% if desc.metadata %}
            <div class="description-metadata mt-4 pt-3 border-top">
                <small class="text-muted">
                    {% if desc.metadata.auto_generated %}
                        <i class="fas fa-robot me-1"></i>
                        Auto-generated description (v{{ desc.metadata.version or '1.0' }})
                    {% else %}
                        <i class="fas fa-user me-1"></i>
                        User-provided description
                    {% endif %}
                    {% if desc.metadata.generated_date or desc.metadata.enhanced_date %}
                        • {{ desc.metadata.generated_date or desc.metadata.enhanced_date }}
                    {% endif %}
                </small>
            </div>
        {% endif %}

    {% else %}
        <!-- Fallback for plain text descriptions -->
        <div class="description-section">
            <h5 class="section-title">
                <i class="fas fa-info-circle text-primary me-2"></i>
                Description
            </h5>
            <p class="section-content">{{ dataset.description or 'No description provided' }}</p>
        </div>
    {% endif %}
</div>

<style>
.structured-description {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.description-section {
    margin-bottom: 1.5rem;
}

.section-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
}

.section-content {
    color: #34495e;
    line-height: 1.6;
    margin-bottom: 0;
}

.stats-card {
    border: 1px solid #e9ecef;
    transition: box-shadow 0.2s ease;
}

.stats-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.stat-value {
    color: #2c3e50;
}

.description-metadata {
    background-color: #f8f9fa;
    padding: 0.75rem;
    border-radius: 0.375rem;
    margin-top: 1rem;
}

@media (max-width: 768px) {
    .section-title {
        font-size: 1rem;
    }
    
    .stats-card {
        margin-top: 1rem;
    }
    
    .stat-item {
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 0.5rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .section-title {
        color: #ecf0f1;
    }
    
    .section-content {
        color: #bdc3c7;
    }
    
    .stats-card {
        background-color: #34495e !important;
        border-color: #4a5568;
    }
    
    .description-metadata {
        background-color: #2d3748;
    }
}
</style>

<script>
// Add interactive features for structured descriptions
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth scrolling for section navigation
    const sectionTitles = document.querySelectorAll('.section-title');
    sectionTitles.forEach(title => {
        title.style.cursor = 'pointer';
        title.addEventListener('click', function() {
            // Toggle section content visibility
            const content = this.nextElementSibling;
            if (content) {
                content.style.display = content.style.display === 'none' ? 'block' : 'none';
            }
        });
    });
    
    // Add copy functionality for statistics
    const statValues = document.querySelectorAll('.stat-value');
    statValues.forEach(stat => {
        stat.addEventListener('click', function() {
            navigator.clipboard.writeText(this.textContent).then(() => {
                // Show brief feedback
                const original = this.textContent;
                this.textContent = 'Copied!';
                setTimeout(() => {
                    this.textContent = original;
                }, 1000);
            });
        });
    });
});
</script>
