from flask_wtf import <PERSON>laskForm
from flask_wtf.file import <PERSON><PERSON><PERSON>, FileAllowed
from wtforms import <PERSON><PERSON><PERSON>, TextAreaField, PasswordField, BooleanField, SubmitField, SelectField
from wtforms.validators import <PERSON>Required, Email, Length, EqualTo, URL, Optional
from app.models.user import User

class LoginForm(FlaskForm):
    """Form for user login"""
    username = <PERSON><PERSON><PERSON>('Username', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])
    remember_me = <PERSON>oleanField('Remember Me')
    submit = SubmitField('Sign In')

class RegistrationForm(FlaskForm):
    """Form for user registration"""
    username = String<PERSON>ield('Username', validators=[DataRequired(), Length(min=3, max=64)])
    email = StringField('Email', validators=[DataRequired(), Email(), Length(max=120)])
    password = Password<PERSON>ield('Password', validators=[<PERSON>Required(), Length(min=8)])
    password2 = PasswordField('Repeat Password', validators=[DataRequired(), EqualTo('password')])
    submit = SubmitField('Register')

    def validate_username(self, username):
        user = User.find_by_username(username.data)
        if user is not None:
            raise ValidationError('Username already taken.')

    def validate_email(self, email):
        user = User.find_by_email(email.data)
        if user is not None:
            raise ValidationError('Email already registered.')

class DatasetForm(FlaskForm):
    """Form for dataset creation or editing with enhanced automatic field extraction"""
    title = StringField('Title (auto-generated if not provided)', validators=[Optional(), Length(max=255)])
    description = TextAreaField('Description (auto-generated if not provided)', validators=[Optional(), Length(max=2000)])

    # File upload field
    dataset_file = FileField('Upload Dataset File', validators=[
        FileAllowed(['csv', 'json', 'xml', 'txt', 'tsv', 'xlsx', 'xls', 'zip'],
                   'Only CSV, JSON, XML, TXT, TSV, Excel, and ZIP files are allowed!')
    ])

    # Alternative URL field
    source_url = StringField('Or Dataset URL', validators=[Optional(), URL()])

    source = StringField('Source (auto-detected if not provided)', validators=[Optional(), Length(max=128)])
    data_type = SelectField('Data Type (auto-detected if not provided)', choices=[
        ('', 'Auto-detect Data Type'),
        ('tabular', 'Tabular Data'),
        ('text', 'Textual Data'),
        ('image', 'Image Data'),
        ('time_series', 'Time Series'),
        ('geo', 'Geospatial Data'),
        ('mixed', 'Mixed Data Types'),
        ('collection', 'Dataset Collection')
    ], validators=[Optional()])
    category = SelectField('Category (auto-detected if not provided)', choices=[
        ('', 'Auto-detect Category'),
        ('education', 'Education'),
        ('health', 'Health'),
        ('agriculture', 'Agriculture'),
        ('environment', 'Environment'),
        ('social_science', 'Social Science'),
        ('economics', 'Economics'),
        ('finance', 'Finance'),
        ('retail', 'Retail'),
        ('technology', 'Technology'),
        ('government', 'Government'),
        ('research', 'Research'),
        ('other', 'Other')
    ], validators=[Optional()])
    tags = StringField('Tags (auto-generated if not provided)', validators=[Optional(), Length(max=500)])

    # Additional optional fields for better metadata
    license = StringField('License (auto-detected if not provided)', validators=[Optional(), Length(max=255)])
    author = StringField('Author/Publisher (defaults to uploader)', validators=[Optional(), Length(max=255)])

    submit = SubmitField('Upload & Process Dataset')

class SearchForm(FlaskForm):
    """Form for dataset search"""
    query = StringField('Search', validators=[Optional()])
    category = SelectField('Category', choices=[
        ('', 'All Categories'),
        ('education', 'Education'),
        ('health', 'Health'),
        ('agriculture', 'Agriculture'),
        ('environment', 'Environment'),
        ('social_science', 'Social Science'),
        ('economics', 'Economics'),
        ('other', 'Other')
    ], validators=[Optional()])
    data_type = SelectField('Data Type', choices=[
        ('', 'All Types'),
        ('tabular', 'Tabular Data'),
        ('text', 'Textual Data'),
        ('image', 'Image Data'),
        ('time_series', 'Time Series'),
        ('geo', 'Geospatial Data'),
        ('mixed', 'Mixed Data Types')
    ], validators=[Optional()])
    submit = SubmitField('Search')

# Import ValidationError after it is used to avoid circular import
from wtforms.validators import ValidationError


class FixQueueForm(FlaskForm):
    """Simple form for fixing stuck queue items"""
    submit = SubmitField('Fix Stuck Items')